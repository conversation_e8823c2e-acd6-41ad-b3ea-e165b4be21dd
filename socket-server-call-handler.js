/**
 * Socket Server Call Handler - Complete WebRTC Call System
 * Handles all call-related socket events for 1-1 audio calling
 */

// Store active calls and timeouts
const activeCalls = new Map(); // callHistoryId -> { caller, receiver, status, startTime, acceptTime }
const callTimeouts = new Map(); // callHistoryId -> timeoutId
const userSockets = new Map(); // userId -> socketId mapping

/**
 * Helper function to emit event to specific user
 * @param {string} userId - Target user ID
 * @param {string} event - Event name
 * @param {object} data - Event data
 */
function emitToUser(userId, event, data) {
  const socketId = userSockets.get(userId);
  if (socketId) {
    const targetSocket = io.sockets.sockets.get(socketId);
    if (targetSocket) {
      targetSocket.emit(event, data);
      console.log(`📤 Emitted ${event} to user ${userId}:`, data);
      return true;
    }
  }
  console.log(`❌ User ${userId} not found or offline`);
  return false;
}

/**
 * Helper function to clear call timeout
 * @param {string} callHistoryId - Call history ID
 */
function clearCallTimeout(callHistoryId) {
  if (callTimeouts.has(callHistoryId)) {
    clearTimeout(callTimeouts.get(callHistoryId));
    callTimeouts.delete(callHistoryId);
    console.log(`⏰ Cleared timeout for call ${callHistoryId}`);
  }
}

/**
 * Helper function to cleanup call resources
 * @param {string} callHistoryId - Call history ID
 */
function cleanupCall(callHistoryId) {
  clearCallTimeout(callHistoryId);
  activeCalls.delete(callHistoryId);
  console.log(`🧹 Cleaned up call ${callHistoryId}`);
}

/**
 * Send push notification for offline users (placeholder)
 * @param {object} data - Notification data
 */
function sendNotificationCall(data) {
  // TODO: Implement push notification logic
  console.log('📱 Sending push notification for call:', data);
}

/**
 * Main socket connection handler
 * @param {object} socket - Socket.io socket instance
 * @param {object} io - Socket.io server instance
 */
function handleCallEvents(socket, io) {
  
  // Store user socket mapping on connection
  socket.on('user-connected', (data) => {
    if (data?.userId) {
      userSockets.set(data.userId, socket.id);
      console.log(`👤 User ${data.userId} connected with socket ${socket.id}`);
    }
  });

  // Clean up on disconnect
  socket.on('disconnect', () => {
    // Remove user from socket mapping
    for (const [userId, socketId] of userSockets.entries()) {
      if (socketId === socket.id) {
        userSockets.delete(userId);
        console.log(`👤 User ${userId} disconnected`);
        break;
      }
    }
  });

  /**
   * Handle ICE candidate exchange
   */
  socket.on("candidate", (data) => {
    try {
      const { candidate, targetUserId, from, callHistoryId } = data;
      
      if (!targetUserId || !from) {
        socket.emit("error", { message: "Invalid candidate data - missing targetUserId or from" });
        return;
      }
      
      // Validate ICE candidate structure
      if (!candidate || (candidate.candidate === undefined && candidate.candidate !== "")) {
        socket.emit("error", { message: "Invalid ICE candidate structure" });
        return;
      }
      
      const success = emitToUser(targetUserId, "candidate", { 
        candidate, 
        from,
        callHistoryId 
      });
      
      if (!success) {
        socket.emit("error", { message: "Target user not available" });
      }
      
    } catch (error) {
      console.error("❌ Error in candidate:", error);
      socket.emit("error", { message: "Internal server error in candidate" });
    }
  });

  /**
   * Handle call initiation
   */
  socket.on("call-user", (data) => {
    try {
      const { targetUserId, from, fromName, fromAvatar, callHistoryId } = data;
      
      if (!targetUserId || !from || !callHistoryId) {
        socket.emit("error", { message: "Invalid call data - missing required fields" });
        return;
      }
      
      // Check if target user is online
      if (!userSockets.has(targetUserId)) {
        console.log(`📱 User ${targetUserId} is offline, sending push notification`);
        sendNotificationCall({ targetUserId, from, fromName, callHistoryId });
        socket.emit("user-offline", { targetUserId });
        return;
      }
      
      // Check if user is already in a call
      for (const [callId, callData] of activeCalls.entries()) {
        if ((callData.caller === targetUserId || callData.receiver === targetUserId) && 
            callData.status !== 'ended') {
          socket.emit("error", { message: "User is already in a call" });
          return;
        }
      }
      
      console.log("📞 Initiating call:", data);
      
      // Track call state
      activeCalls.set(callHistoryId, {
        caller: from,
        receiver: targetUserId,
        status: 'ringing',
        startTime: Date.now()
      });
      
      // Set timeout 60s cho cuộc gọi
      const timeoutId = setTimeout(() => {
        console.log(`⏰ Call ${callHistoryId} timed out`);
        
        // Emit timeout event to both users
        emitToUser(from, "call-timeout", { 
          targetUserId,
          callHistoryId,
          reason: 'no-answer'
        });
        emitToUser(targetUserId, "call-timeout", { 
          from,
          callHistoryId,
          reason: 'no-answer'
        });
        
        // Cleanup call resources
        cleanupCall(callHistoryId);
      }, 60000); // 60 seconds
      
      callTimeouts.set(callHistoryId, timeoutId);
      
      // Send incoming call notification
      const success = emitToUser(targetUserId, "incoming-call", { 
        from, 
        socketId: socket.id, 
        fromName, 
        fromAvatar, 
        callHistoryId 
      });
      
      if (!success) {
        cleanupCall(callHistoryId);
        socket.emit("error", { message: "Failed to reach target user" });
      }
      
    } catch (error) {
      console.error("❌ Error in call-user:", error);
      socket.emit("error", { message: "Internal server error in call-user" });
    }
  });

  /**
   * Handle call acceptance
   */
  socket.on("accept-call", (data) => {
    try {
      const { targetUserId, from, callHistoryId } = data; // targetUserId = caller, from = receiver
      
      if (!targetUserId || !from || !callHistoryId) {
        socket.emit("error", { message: "Invalid accept-call data" });
        return;
      }
      
      // Clear timeout
      clearCallTimeout(callHistoryId);
      
      // Update call state
      if (activeCalls.has(callHistoryId)) {
        const callData = activeCalls.get(callHistoryId);
        callData.status = 'connected';
        callData.acceptTime = Date.now();
        console.log(`✅ Call ${callHistoryId} accepted by ${from}`);
      }
      
      // Notify caller that call was accepted
      const success = emitToUser(targetUserId, "accept-call", { 
        from, // receiver ID
        acceptedBy: from, // receiver ID  
        callHistoryId 
      });
      
      if (!success) {
        socket.emit("error", { message: "Failed to notify caller" });
        cleanupCall(callHistoryId);
      }
      
    } catch (error) {
      console.error("❌ Error in accept-call:", error);
      socket.emit("error", { message: "Internal server error in accept-call" });
    }
  });

  /**
   * Handle call rejection
   */
  socket.on("reject-call", (data) => {
    try {
      const { from, callHistoryId } = data; // from = caller ID
      
      if (!from || !callHistoryId) {
        socket.emit("error", { message: "Invalid reject-call data" });
        return;
      }
      
      // Clear timeout
      clearCallTimeout(callHistoryId);
      
      // Update call state
      if (activeCalls.has(callHistoryId)) {
        activeCalls.get(callHistoryId).status = 'rejected';
        console.log(`❌ Call ${callHistoryId} rejected`);
      }
      
      // Notify caller that call was rejected
      const success = emitToUser(from, "reject-call", { 
        from, 
        callHistoryId,
        rejectedBy: data.rejectedBy || 'unknown'
      });
      
      // Cleanup call resources
      cleanupCall(callHistoryId);
      
      if (!success) {
        console.log(`⚠️ Failed to notify caller ${from} about rejection`);
      }
      
    } catch (error) {
      console.error("❌ Error in reject-call:", error);
      socket.emit("error", { message: "Internal server error in reject-call" });
    }
  });

  /**
   * Handle WebRTC offer
   */
  socket.on("offer", (data) => {
    try {
      const { to, offer, from, callHistoryId } = data;
      
      if (!to || !offer || !from) {
        socket.emit("error", { message: "Invalid offer data - missing required fields" });
        return;
      }
      
      // Validate SDP offer structure
      if (!offer.type || !offer.sdp) {
        socket.emit("error", { message: "Invalid SDP offer structure" });
        return;
      }
      
      if (offer.type !== 'offer') {
        socket.emit("error", { message: "Invalid offer type" });
        return;
      }
      
      console.log(`📤 Forwarding offer from ${from} to ${to}`);
      
      const success = emitToUser(to, "offer", { 
        offer, 
        from,
        callHistoryId 
      });
      
      if (!success) {
        socket.emit("error", { message: "Failed to deliver offer to target user" });
      }
      
    } catch (error) {
      console.error("❌ Error in offer:", error);
      socket.emit("error", { message: "Internal server error in offer" });
    }
  });

  /**
   * Handle WebRTC answer
   */
  socket.on("answer", (data) => {
    try {
      const { to, answer, from, callHistoryId } = data;
      
      if (!to || !answer || !from) {
        socket.emit("error", { message: "Invalid answer data - missing required fields" });
        return;
      }
      
      // Validate SDP answer structure
      if (!answer.type || !answer.sdp) {
        socket.emit("error", { message: "Invalid SDP answer structure" });
        return;
      }
      
      if (answer.type !== 'answer') {
        socket.emit("error", { message: "Invalid answer type" });
        return;
      }
      
      console.log(`📤 Forwarding answer from ${from} to ${to}`);
      
      const success = emitToUser(to, "answer", { 
        answer, 
        from,
        callHistoryId 
      });
      
      if (!success) {
        socket.emit("error", { message: "Failed to deliver answer to target user" });
      }
      
    } catch (error) {
      console.error("❌ Error in answer:", error);
      socket.emit("error", { message: "Internal server error in answer" });
    }
  });

  /**
   * Handle call termination
   */
  socket.on("end-call", (data) => {
    try {
      const { targetUserId, from, callHistoryId } = data;
      
      if (!targetUserId || !from) {
        socket.emit("error", { message: "Invalid end-call data" });
        return;
      }
      
      console.log(`📞 Ending call ${callHistoryId} from ${from} to ${targetUserId}`);
      
      // Update call state
      if (callHistoryId && activeCalls.has(callHistoryId)) {
        const callData = activeCalls.get(callHistoryId);
        callData.status = 'ended';
        callData.endTime = Date.now();
        
        // Calculate call duration if call was connected
        if (callData.acceptTime) {
          callData.duration = callData.endTime - callData.acceptTime;
          console.log(`📊 Call duration: ${callData.duration}ms`);
        }
      }
      
      // Notify other user about call end
      const success = emitToUser(targetUserId, "end-call", { 
        from, // người kết thúc cuộc gọi
        callHistoryId,
        endedBy: from
      });
      
      // Cleanup call resources
      if (callHistoryId) {
        cleanupCall(callHistoryId);
      }
      
      if (!success) {
        console.log(`⚠️ Failed to notify user ${targetUserId} about call end`);
      }
      
    } catch (error) {
      console.error("❌ Error in end-call:", error);
      socket.emit("error", { message: "Internal server error in end-call" });
    }
  });

  /**
   * Handle call status query
   */
  socket.on("get-call-status", (data) => {
    try {
      const { callHistoryId } = data;
      
      if (!callHistoryId) {
        socket.emit("error", { message: "Invalid call status query" });
        return;
      }
      
      const callData = activeCalls.get(callHistoryId);
      socket.emit("call-status", {
        callHistoryId,
        status: callData ? callData.status : 'not-found',
        data: callData || null
      });
      
    } catch (error) {
      console.error("❌ Error in get-call-status:", error);
      socket.emit("error", { message: "Internal server error in get-call-status" });
    }
  });
}
/**
 * Get active calls statistics
 */
function getCallStats() {
  const stats = {
    totalActiveCalls: activeCalls.size,
    totalTimeouts: callTimeouts.size,
    onlineUsers: userSockets.size,
    callsByStatus: {}
  };

  // Count calls by status
  for (const callData of activeCalls.values()) {
    stats.callsByStatus[callData.status] = (stats.callsByStatus[callData.status] || 0) + 1;
  }

  return stats;
}

/**
 * Force cleanup expired calls (cleanup utility)
 */
function cleanupExpiredCalls() {
  const now = Date.now();
  const expiredCalls = [];

  for (const [callId, callData] of activeCalls.entries()) {
    // Cleanup calls older than 5 minutes
    if (now - callData.startTime > 5 * 60 * 1000) {
      expiredCalls.push(callId);
    }
  }

  expiredCalls.forEach(callId => {
    console.log(`🧹 Force cleaning expired call: ${callId}`);
    cleanupCall(callId);
  });

  return expiredCalls.length;
}

// Export functions and data
module.exports = {
  handleCallEvents,
  activeCalls,
  callTimeouts,
  userSockets,
  getCallStats,
  cleanupExpiredCalls
};
