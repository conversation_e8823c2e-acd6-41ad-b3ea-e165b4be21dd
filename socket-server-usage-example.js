/**
 * Socket Server Usage Example
 * How to integrate the call handler with your existing socket server
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const { handleCallEvents, getCallStats, cleanupExpiredCalls } = require('./socket-server-call-handler');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Cleanup expired calls every 5 minutes
setInterval(() => {
  const cleanedCount = cleanupExpiredCalls();
  if (cleanedCount > 0) {
    console.log(`🧹 Cleaned up ${cleanedCount} expired calls`);
  }
}, 5 * 60 * 1000);

// Socket connection handler
io.on('connection', (socket) => {
  console.log(`🔌 New socket connection: ${socket.id}`);
  
  // Handle authentication/user connection
  socket.on('authenticate', (data) => {
    const { CustomerId, Name } = data;
    
    if (CustomerId && Name) {
      // Store user info in socket
      socket.userId = CustomerId;
      socket.userName = Name;
      
      // Emit user-connected event to initialize call handler
      socket.emit('user-connected', { userId: CustomerId, name: Name });
      
      console.log(`👤 User authenticated: ${Name} (${CustomerId})`);
      
      // Join user to their personal room for direct messaging
      socket.join(`user_${CustomerId}`);
      
      // Broadcast user online status
      socket.broadcast.emit('user-online', CustomerId);
    }
  });
  
  // Initialize call event handlers
  handleCallEvents(socket, io);
  
  // Your existing chat/message handlers
  socket.on('join-rooms', (data) => {
    const { roomId } = data;
    socket.join(roomId);
    console.log(`📝 User ${socket.userId} joined room: ${roomId}`);
  });
  
  socket.on('send-message', (data) => {
    const { roomId, message, targetCustomerId } = data;
    // Your message handling logic
    io.to(roomId).emit('receive-message', {
      roomId,
      fromUserId: socket.userId,
      message
    });
  });
  
  // Handle disconnect
  socket.on('disconnect', () => {
    console.log(`🔌 Socket disconnected: ${socket.id}`);
    
    if (socket.userId) {
      // Broadcast user offline status
      socket.broadcast.emit('user-offline', socket.userId);
      console.log(`👤 User ${socket.userName} (${socket.userId}) went offline`);
    }
  });
  
  // Admin endpoint to get call statistics
  socket.on('get-call-stats', () => {
    if (socket.userId === 'admin') { // Add your admin check logic
      const stats = getCallStats();
      socket.emit('call-stats', stats);
    }
  });
});

// REST API endpoints (optional)
app.get('/api/call-stats', (req, res) => {
  const stats = getCallStats();
  res.json(stats);
});

app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    connections: io.engine.clientsCount
  });
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`🚀 Socket server running on port ${PORT}`);
});

/**
 * Example client-side events that your React Native app should emit:
 * 
 * 1. Authentication:
 *    socket.emit('authenticate', { CustomerId: 'user123', Name: 'John Doe' });
 * 
 * 2. Start a call:
 *    socket.emit('call-user', {
 *      targetUserId: 'user456',
 *      from: 'user123',
 *      fromName: 'John Doe',
 *      fromAvatar: 'avatar_url',
 *      callHistoryId: 'call_123'
 *    });
 * 
 * 3. Accept a call:
 *    socket.emit('accept-call', {
 *      targetUserId: 'user123', // caller
 *      from: 'user456',         // receiver
 *      callHistoryId: 'call_123'
 *    });
 * 
 * 4. Reject a call:
 *    socket.emit('reject-call', {
 *      from: 'user123',         // caller
 *      callHistoryId: 'call_123',
 *      rejectedBy: 'user456'
 *    });
 * 
 * 5. WebRTC Signaling:
 *    socket.emit('offer', {
 *      to: 'user456',
 *      from: 'user123',
 *      offer: { type: 'offer', sdp: '...' },
 *      callHistoryId: 'call_123'
 *    });
 * 
 *    socket.emit('answer', {
 *      to: 'user123',
 *      from: 'user456', 
 *      answer: { type: 'answer', sdp: '...' },
 *      callHistoryId: 'call_123'
 *    });
 * 
 *    socket.emit('candidate', {
 *      targetUserId: 'user456',
 *      from: 'user123',
 *      candidate: { candidate: '...', sdpMLineIndex: 0, sdpMid: 'audio' },
 *      callHistoryId: 'call_123'
 *    });
 * 
 * 6. End call:
 *    socket.emit('end-call', {
 *      targetUserId: 'user456',
 *      from: 'user123',
 *      callHistoryId: 'call_123'
 *    });
 */

module.exports = { app, server, io };
