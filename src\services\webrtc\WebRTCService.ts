/**
 * WebRTC Service Core
 * Handles WebRTC peer connection, media streams, and signaling for 1-1 audio calls
 */

import {
  RTCPeerConnection,
  RTCSessionDescription,
  RTCIceCandidate,
  mediaDevices,
  MediaStream,
  RTCConfiguration,
} from 'react-native-webrtc';

import {
  CallStatus,
  CallDirection,
  CallData,
  UserInfo,
  WebRTCConnectionState,
  CallEventCallbacks,
  WebRTCConfig,
  WebRTCError,
  WebRTCErrorInfo,
  DEFAULT_WEBRTC_CONFIG,
  CallTimer,
  CallControlsState,
  AudioDevice,
} from './types/WebRTCTypes';

import SocketService from '../../modules/chat/services/SocketService';

class WebRTCService {
  // Core WebRTC state
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: MediaStream | null = null;
  private remoteStream: MediaStream | null = null;
  
  // Call state
  private currentCall: CallData | null = null;
  private callStatus: CallStatus = CallStatus.IDLE;
  private callTimer: CallTimer = { startTime: 0, duration: 0, isRunning: false };
  
  // Configuration
  private config: WebRTCConfig = DEFAULT_WEBRTC_CONFIG;
  
  // Event callbacks
  private callbacks: CallEventCallbacks = {};
  
  // Call controls
  private callControls: CallControlsState = {
    isMuted: false,
    isSpeakerOn: false,
    currentAudioDevice: AudioDevice.EARPIECE,
    availableAudioDevices: [AudioDevice.EARPIECE, AudioDevice.SPEAKER],
  };

  // Timer interval
  private timerInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.initializeSocketListeners();
  }

  /**
   * Initialize socket event listeners for WebRTC signaling
   */
  private initializeSocketListeners(): void {
    const socket = SocketService.getSocket();
    if (!socket) {
      console.warn('⚠️ Socket not available for WebRTC listeners');
      return;
    }

    // Listen for incoming calls
    socket.on('incoming-call', this.handleIncomingCall.bind(this));
    
    // Listen for call acceptance
    socket.on('accept-call', this.handleCallAccepted.bind(this));
    
    // Listen for call rejection
    socket.on('reject-call', this.handleCallRejected.bind(this));
    
    // Listen for call end
    socket.on('end-call', this.handleCallEnded.bind(this));
    
    // Listen for call timeout
    socket.on('call-timeout', this.handleCallTimeout.bind(this));
    
    // WebRTC signaling events
    socket.on('offer', this.handleOffer.bind(this));
    socket.on('answer', this.handleAnswer.bind(this));
    socket.on('candidate', this.handleCandidate.bind(this));

    console.log('✅ WebRTC socket listeners initialized');
  }

  /**
   * Set event callbacks
   */
  public setCallbacks(callbacks: CallEventCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * Get current call data
   */
  public getCurrentCall(): CallData | null {
    return this.currentCall;
  }

  /**
   * Get current call status
   */
  public getCallStatus(): CallStatus {
    return this.callStatus;
  }

  /**
   * Get call timer
   */
  public getCallTimer(): CallTimer {
    return { ...this.callTimer };
  }

  /**
   * Get call controls state
   */
  public getCallControls(): CallControlsState {
    return { ...this.callControls };
  }

  /**
   * Start an outgoing call
   */
  public async startCall(targetUser: UserInfo, currentUser: UserInfo): Promise<void> {
    try {
      console.log(`📞 Starting call to ${targetUser.name} (${targetUser.id})`);

      if (this.callStatus !== CallStatus.IDLE) {
        throw new Error('Already in a call');
      }

      // Generate call history ID
      const callHistoryId = this.generateCallHistoryId();

      // Create call data
      this.currentCall = {
        callHistoryId,
        caller: currentUser,
        receiver: targetUser,
        direction: CallDirection.OUTGOING,
        status: CallStatus.CALLING,
        startTime: Date.now(),
      };

      // Update status
      this.updateCallStatus(CallStatus.CALLING);

      // Initialize peer connection
      await this.initializePeerConnection();

      // Get local media stream
      await this.getLocalMediaStream();

      // Send call invitation via socket
      const socket = SocketService.getSocket();
      if (socket) {
        socket.emit('call-user', {
          targetUserId: targetUser.id,
          from: currentUser.id,
          fromName: currentUser.name,
          fromAvatar: currentUser.avatar,
          callHistoryId,
        });
      } else {
        throw new Error('Socket not connected');
      }

      console.log(`✅ Call initiated to ${targetUser.name}`);
    } catch (error) {
      console.error('❌ Error starting call:', error);
      this.handleCallError(WebRTCError.CONNECTION_FAILED, error);
    }
  }

  /**
   * Accept an incoming call
   */
  public async acceptCall(): Promise<void> {
    try {
      console.log('📞 Accepting incoming call');

      if (!this.currentCall || this.callStatus !== CallStatus.RINGING) {
        throw new Error('No incoming call to accept');
      }

      // Update status
      this.updateCallStatus(CallStatus.CONNECTING);

      // Initialize peer connection if not already done
      if (!this.peerConnection) {
        await this.initializePeerConnection();
      }

      // Get local media stream
      await this.getLocalMediaStream();

      // Send accept call via socket
      const socket = SocketService.getSocket();
      if (socket) {
        socket.emit('accept-call', {
          targetUserId: this.currentCall.caller.id,
          from: this.currentCall.receiver.id,
          callHistoryId: this.currentCall.callHistoryId,
        });
      }

      // Update call data
      this.currentCall.acceptTime = Date.now();
      this.currentCall.status = CallStatus.CONNECTING;

      console.log('✅ Call accepted');
    } catch (error) {
      console.error('❌ Error accepting call:', error);
      this.handleCallError(WebRTCError.CONNECTION_FAILED, error);
    }
  }

  /**
   * Reject an incoming call
   */
  public rejectCall(): void {
    try {
      console.log('📞 Rejecting incoming call');

      if (!this.currentCall || this.callStatus !== CallStatus.RINGING) {
        console.warn('⚠️ No incoming call to reject');
        return;
      }

      // Send reject call via socket
      const socket = SocketService.getSocket();
      if (socket) {
        socket.emit('reject-call', {
          from: this.currentCall.caller.id,
          callHistoryId: this.currentCall.callHistoryId,
          rejectedBy: this.currentCall.receiver.id,
        });
      }

      // End call locally
      this.endCall();

      console.log('✅ Call rejected');
    } catch (error) {
      console.error('❌ Error rejecting call:', error);
    }
  }

  /**
   * End current call
   */
  public endCall(): void {
    try {
      console.log('📞 Ending call');

      if (this.callStatus === CallStatus.IDLE) {
        console.warn('⚠️ No active call to end');
        return;
      }

      // Send end call via socket if we have an active call
      if (this.currentCall) {
        const socket = SocketService.getSocket();
        if (socket) {
          const targetUserId = this.currentCall.direction === CallDirection.OUTGOING 
            ? this.currentCall.receiver.id 
            : this.currentCall.caller.id;

          const fromUserId = this.currentCall.direction === CallDirection.OUTGOING
            ? this.currentCall.caller.id
            : this.currentCall.receiver.id;

          socket.emit('end-call', {
            targetUserId,
            from: fromUserId,
            callHistoryId: this.currentCall.callHistoryId,
          });
        }

        // Update call data
        this.currentCall.endTime = Date.now();
        if (this.currentCall.acceptTime) {
          this.currentCall.duration = this.currentCall.endTime - this.currentCall.acceptTime;
        }
      }

      // Cleanup resources
      this.cleanup();

      // Update status
      this.updateCallStatus(CallStatus.ENDED);

      console.log('✅ Call ended');
    } catch (error) {
      console.error('❌ Error ending call:', error);
    }
  }

  /**
   * Toggle mute
   */
  public toggleMute(): boolean {
    try {
      if (this.localStream) {
        const audioTrack = this.localStream.getAudioTracks()[0];
        if (audioTrack) {
          audioTrack.enabled = !audioTrack.enabled;
          this.callControls.isMuted = !audioTrack.enabled;
          console.log(`🔇 Mute toggled: ${this.callControls.isMuted}`);
          return this.callControls.isMuted;
        }
      }
      return false;
    } catch (error) {
      console.error('❌ Error toggling mute:', error);
      return false;
    }
  }

  /**
   * Toggle speaker
   */
  public toggleSpeaker(): boolean {
    try {
      // This will be implemented with native module calls
      this.callControls.isSpeakerOn = !this.callControls.isSpeakerOn;
      this.callControls.currentAudioDevice = this.callControls.isSpeakerOn 
        ? AudioDevice.SPEAKER 
        : AudioDevice.EARPIECE;
      
      console.log(`🔊 Speaker toggled: ${this.callControls.isSpeakerOn}`);
      return this.callControls.isSpeakerOn;
    } catch (error) {
      console.error('❌ Error toggling speaker:', error);
      return false;
    }
  }

  /**
   * Initialize WebRTC peer connection
   */
  private async initializePeerConnection(): Promise<void> {
    try {
      console.log('🔗 Initializing peer connection');

      const configuration: RTCConfiguration = {
        iceServers: this.config.iceServers,
      };

      this.peerConnection = new RTCPeerConnection(configuration);

      // Set up event handlers (react-native-webrtc style)
      (this.peerConnection as any).onicecandidate = (event: any) => {
        if (event.candidate && this.currentCall) {
          console.log('🧊 ICE candidate generated');
          this.sendIceCandidate(event.candidate);
        }
      };

      (this.peerConnection as any).onaddstream = (event: any) => {
        console.log('📡 Remote stream received');
        this.remoteStream = event.stream;

        if (this.callbacks.onRemoteStreamReceived) {
          this.callbacks.onRemoteStreamReceived(event.stream);
        }
      };

      (this.peerConnection as any).oniceconnectionstatechange = () => {
        const state = this.peerConnection?.iceConnectionState;
        console.log(`🧊 ICE connection state: ${state}`);

        if (state === 'connected' || state === 'completed') {
          this.updateCallStatus(CallStatus.CONNECTED);
          this.startCallTimer();
        } else if (state === 'failed' || state === 'disconnected') {
          this.handleCallError(WebRTCError.CONNECTION_FAILED, new Error(`ICE connection ${state}`));
        }

        if (this.callbacks.onConnectionStateChanged) {
          this.callbacks.onConnectionStateChanged(state || 'unknown');
        }
      };

      console.log('✅ Peer connection initialized');
    } catch (error) {
      console.error('❌ Error initializing peer connection:', error);
      throw error;
    }
  }

  /**
   * Get local media stream (audio only)
   */
  private async getLocalMediaStream(): Promise<void> {
    try {
      console.log('🎤 Getting local media stream');

      const constraints = {
        audio: this.config.audioConstraints || true,
        video: false, // Audio only
      };

      this.localStream = await mediaDevices.getUserMedia(constraints);

      // Add stream to peer connection
      if (this.peerConnection && this.localStream) {
        this.peerConnection.addStream(this.localStream);
        console.log('✅ Local stream added to peer connection');
      }

    } catch (error) {
      console.error('❌ Error getting local media stream:', error);
      this.handleCallError(WebRTCError.MEDIA_ERROR, error);
      throw error;
    }
  }

  /**
   * Send ICE candidate via socket
   */
  private sendIceCandidate(candidate: RTCIceCandidate): void {
    if (!this.currentCall) return;

    const socket = SocketService.getSocket();
    if (socket) {
      const targetUserId = this.currentCall.direction === CallDirection.OUTGOING
        ? this.currentCall.receiver.id
        : this.currentCall.caller.id;

      const fromUserId = this.currentCall.direction === CallDirection.OUTGOING
        ? this.currentCall.caller.id
        : this.currentCall.receiver.id;

      socket.emit('candidate', {
        targetUserId,
        from: fromUserId,
        candidate,
        callHistoryId: this.currentCall.callHistoryId,
      });
    }
  }

  /**
   * Start call timer
   */
  private startCallTimer(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }

    this.callTimer = {
      startTime: Date.now(),
      duration: 0,
      isRunning: true,
    };

    this.timerInterval = setInterval(() => {
      if (this.callTimer.isRunning) {
        this.callTimer.duration = Math.floor((Date.now() - this.callTimer.startTime) / 1000);
      }
    }, 1000);

    console.log('⏱️ Call timer started');
  }

  /**
   * Stop call timer
   */
  private stopCallTimer(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
    this.callTimer.isRunning = false;
    console.log(`⏱️ Call timer stopped. Duration: ${this.callTimer.duration}s`);
  }

  /**
   * Cleanup resources
   */
  private cleanup(): void {
    console.log('🧹 Cleaning up WebRTC resources');

    // Stop timer
    this.stopCallTimer();

    // Close peer connection
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    // Stop local stream
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    // Clear remote stream
    this.remoteStream = null;

    // Reset call controls
    this.callControls = {
      isMuted: false,
      isSpeakerOn: false,
      currentAudioDevice: AudioDevice.EARPIECE,
      availableAudioDevices: [AudioDevice.EARPIECE, AudioDevice.SPEAKER],
    };

    console.log('✅ WebRTC resources cleaned up');
  }

  /**
   * Generate unique call history ID
   */
  private generateCallHistoryId(): string {
    return `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Update call status and notify callbacks
   */
  private updateCallStatus(status: CallStatus): void {
    this.callStatus = status;
    if (this.currentCall) {
      this.currentCall.status = status;
    }

    // Notify callback
    if (this.callbacks.onCallStatusChanged && this.currentCall) {
      this.callbacks.onCallStatusChanged(status, this.currentCall);
    }

    console.log(`📊 Call status updated: ${status}`);
  }

  /**
   * Handle call error
   */
  private handleCallError(errorType: WebRTCError, originalError: any): void {
    const errorInfo: WebRTCErrorInfo = {
      type: errorType,
      message: originalError?.message || 'Unknown error',
      originalError,
    };

    console.error('❌ WebRTC Error:', errorInfo);

    // Update status
    this.updateCallStatus(CallStatus.FAILED);

    // Notify callback
    if (this.callbacks.onCallFailed && this.currentCall) {
      this.callbacks.onCallFailed(errorInfo.message, this.currentCall);
    }

    // Cleanup
    this.cleanup();
  }

  // Socket Event Handlers

  /**
   * Handle incoming call event
   */
  private handleIncomingCall(data: any): void {
    try {
      console.log('📞 Incoming call received:', data);

      if (this.callStatus !== CallStatus.IDLE) {
        console.warn('⚠️ Already in a call, rejecting incoming call');
        // Auto-reject if already in call
        const socket = SocketService.getSocket();
        if (socket) {
          socket.emit('reject-call', {
            from: data.from,
            callHistoryId: data.callHistoryId,
            rejectedBy: 'busy',
          });
        }
        return;
      }

      // Create call data for incoming call
      this.currentCall = {
        callHistoryId: data.callHistoryId,
        caller: {
          id: data.from,
          name: data.fromName,
          avatar: data.fromAvatar,
        },
        receiver: {
          id: '', // Will be set when we know current user
          name: '',
        },
        direction: CallDirection.INCOMING,
        status: CallStatus.RINGING,
        startTime: Date.now(),
      };

      // Update status
      this.updateCallStatus(CallStatus.RINGING);

      console.log(`✅ Incoming call from ${data.fromName} (${data.from})`);
    } catch (error) {
      console.error('❌ Error handling incoming call:', error);
    }
  }

  /**
   * Handle call accepted event
   */
  private handleCallAccepted(data: any): void {
    try {
      console.log('✅ Call accepted:', data);

      if (!this.currentCall || this.callStatus !== CallStatus.CALLING) {
        console.warn('⚠️ No outgoing call to accept');
        return;
      }

      // Update status
      this.updateCallStatus(CallStatus.CONNECTING);

      // Create and send offer
      this.createAndSendOffer();

      console.log('📤 Call accepted, creating offer');
    } catch (error) {
      console.error('❌ Error handling call accepted:', error);
      this.handleCallError(WebRTCError.SIGNALING_ERROR, error);
    }
  }

  /**
   * Handle call rejected event
   */
  private handleCallRejected(data: any): void {
    try {
      console.log('❌ Call rejected:', data);

      if (this.callbacks.onCallEnded && this.currentCall) {
        this.callbacks.onCallEnded('rejected', this.currentCall);
      }

      // Cleanup
      this.cleanup();
      this.updateCallStatus(CallStatus.ENDED);
      this.currentCall = null;

      console.log('📞 Call rejected by remote user');
    } catch (error) {
      console.error('❌ Error handling call rejected:', error);
    }
  }

  /**
   * Handle call ended event
   */
  private handleCallEnded(data: any): void {
    try {
      console.log('📞 Call ended by remote user:', data);

      if (this.callbacks.onCallEnded && this.currentCall) {
        this.callbacks.onCallEnded('ended_by_remote', this.currentCall);
      }

      // Cleanup
      this.cleanup();
      this.updateCallStatus(CallStatus.ENDED);
      this.currentCall = null;

      console.log('✅ Call ended');
    } catch (error) {
      console.error('❌ Error handling call ended:', error);
    }
  }

  /**
   * Handle call timeout event
   */
  private handleCallTimeout(data: any): void {
    try {
      console.log('⏰ Call timeout:', data);

      if (this.callbacks.onCallEnded && this.currentCall) {
        this.callbacks.onCallEnded('timeout', this.currentCall);
      }

      // Cleanup
      this.cleanup();
      this.updateCallStatus(CallStatus.TIMEOUT);
      this.currentCall = null;

      console.log('⏰ Call timed out');
    } catch (error) {
      console.error('❌ Error handling call timeout:', error);
    }
  }

  /**
   * Handle WebRTC offer
   */
  private async handleOffer(data: any): Promise<void> {
    try {
      console.log('📤 Received offer:', data);

      if (!this.peerConnection) {
        console.warn('⚠️ No peer connection for offer');
        return;
      }

      // Set remote description
      await this.peerConnection.setRemoteDescription(new RTCSessionDescription(data.offer));

      // Create and send answer
      await this.createAndSendAnswer();

      console.log('✅ Offer processed, answer sent');
    } catch (error) {
      console.error('❌ Error handling offer:', error);
      this.handleCallError(WebRTCError.SIGNALING_ERROR, error);
    }
  }

  /**
   * Handle WebRTC answer
   */
  private async handleAnswer(data: any): Promise<void> {
    try {
      console.log('📥 Received answer:', data);

      if (!this.peerConnection) {
        console.warn('⚠️ No peer connection for answer');
        return;
      }

      // Set remote description
      await this.peerConnection.setRemoteDescription(new RTCSessionDescription(data.answer));

      console.log('✅ Answer processed');
    } catch (error) {
      console.error('❌ Error handling answer:', error);
      this.handleCallError(WebRTCError.SIGNALING_ERROR, error);
    }
  }

  /**
   * Handle ICE candidate
   */
  private async handleCandidate(data: any): Promise<void> {
    try {
      console.log('🧊 Received ICE candidate:', data);

      if (!this.peerConnection) {
        console.warn('⚠️ No peer connection for candidate');
        return;
      }

      // Add ICE candidate
      await this.peerConnection.addIceCandidate(new RTCIceCandidate(data.candidate));

      console.log('✅ ICE candidate added');
    } catch (error) {
      console.error('❌ Error handling ICE candidate:', error);
      // Don't fail the call for ICE candidate errors
    }
  }

  // WebRTC Signaling Methods

  /**
   * Create and send WebRTC offer
   */
  private async createAndSendOffer(): Promise<void> {
    try {
      if (!this.peerConnection || !this.currentCall) {
        throw new Error('No peer connection or current call');
      }

      console.log('📤 Creating offer');

      // Create offer
      const offer = await this.peerConnection.createOffer({
        offerToReceiveAudio: true,
        offerToReceiveVideo: false,
      });

      // Set local description
      await this.peerConnection.setLocalDescription(offer);

      // Send offer via socket
      const socket = SocketService.getSocket();
      if (socket) {
        socket.emit('offer', {
          to: this.currentCall.receiver.id,
          from: this.currentCall.caller.id,
          offer,
          callHistoryId: this.currentCall.callHistoryId,
        });
      }

      console.log('✅ Offer created and sent');
    } catch (error) {
      console.error('❌ Error creating offer:', error);
      this.handleCallError(WebRTCError.SIGNALING_ERROR, error);
    }
  }

  /**
   * Create and send WebRTC answer
   */
  private async createAndSendAnswer(): Promise<void> {
    try {
      if (!this.peerConnection || !this.currentCall) {
        throw new Error('No peer connection or current call');
      }

      console.log('📥 Creating answer');

      // Create answer
      const answer = await this.peerConnection.createAnswer();

      // Set local description
      await this.peerConnection.setLocalDescription(answer);

      // Send answer via socket
      const socket = SocketService.getSocket();
      if (socket) {
        socket.emit('answer', {
          to: this.currentCall.caller.id,
          from: this.currentCall.receiver.id,
          answer,
          callHistoryId: this.currentCall.callHistoryId,
        });
      }

      console.log('✅ Answer created and sent');
    } catch (error) {
      console.error('❌ Error creating answer:', error);
      this.handleCallError(WebRTCError.SIGNALING_ERROR, error);
    }
  }
}

// Export singleton instance
export default new WebRTCService();
