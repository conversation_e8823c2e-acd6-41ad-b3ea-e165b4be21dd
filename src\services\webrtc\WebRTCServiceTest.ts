/**
 * WebRTC Service Test
 * Simple test functions to verify WebRTC Service functionality
 */

import WebRTCService from './WebRTCService';
import { CallStatus, CallDirection, UserInfo } from './types/WebRTCTypes';

/**
 * Test WebRTC Service basic functionality
 */
export class WebRTCServiceTest {
  
  /**
   * Test service initialization
   */
  static testInitialization(): boolean {
    try {
      console.log('🧪 Testing WebRTC Service initialization...');
      
      // Check initial state
      const initialStatus = WebRTCService.getCallStatus();
      const initialCall = WebRTCService.getCurrentCall();
      const initialTimer = WebRTCService.getCallTimer();
      const initialControls = WebRTCService.getCallControls();
      
      console.log('📊 Initial state:', {
        status: initialStatus,
        call: initialCall,
        timer: initialTimer,
        controls: initialControls,
      });
      
      // Verify initial state
      if (initialStatus !== CallStatus.IDLE) {
        throw new Error(`Expected status IDLE, got ${initialStatus}`);
      }
      
      if (initialCall !== null) {
        throw new Error(`Expected null call, got ${initialCall}`);
      }
      
      if (initialTimer.isRunning) {
        throw new Error('Timer should not be running initially');
      }
      
      console.log('✅ WebRTC Service initialization test passed');
      return true;
      
    } catch (error) {
      console.error('❌ WebRTC Service initialization test failed:', error);
      return false;
    }
  }
  
  /**
   * Test call controls
   */
  static testCallControls(): boolean {
    try {
      console.log('🧪 Testing call controls...');
      
      // Test mute toggle
      const initialMute = WebRTCService.getCallControls().isMuted;
      const muteResult = WebRTCService.toggleMute();
      const afterMute = WebRTCService.getCallControls().isMuted;
      
      console.log('🔇 Mute test:', { initialMute, muteResult, afterMute });
      
      // Test speaker toggle
      const initialSpeaker = WebRTCService.getCallControls().isSpeakerOn;
      const speakerResult = WebRTCService.toggleSpeaker();
      const afterSpeaker = WebRTCService.getCallControls().isSpeakerOn;
      
      console.log('🔊 Speaker test:', { initialSpeaker, speakerResult, afterSpeaker });
      
      console.log('✅ Call controls test passed');
      return true;
      
    } catch (error) {
      console.error('❌ Call controls test failed:', error);
      return false;
    }
  }
  
  /**
   * Test callback registration
   */
  static testCallbacks(): boolean {
    try {
      console.log('🧪 Testing callback registration...');
      
      let callbackCalled = false;
      
      // Register callbacks
      WebRTCService.setCallbacks({
        onCallStatusChanged: (status, callData) => {
          console.log('📞 Status changed callback:', status, callData);
          callbackCalled = true;
        },
        onRemoteStreamReceived: (stream) => {
          console.log('📡 Remote stream callback:', stream);
        },
        onCallEnded: (reason, callData) => {
          console.log('📞 Call ended callback:', reason, callData);
        },
        onCallFailed: (error, callData) => {
          console.log('❌ Call failed callback:', error, callData);
        },
        onConnectionStateChanged: (state) => {
          console.log('🔗 Connection state callback:', state);
        },
      });
      
      console.log('✅ Callback registration test passed');
      return true;
      
    } catch (error) {
      console.error('❌ Callback registration test failed:', error);
      return false;
    }
  }
  
  /**
   * Test user data creation
   */
  static testUserData(): boolean {
    try {
      console.log('🧪 Testing user data creation...');
      
      const testUser1: UserInfo = {
        id: 'user123',
        name: 'John Doe',
        avatar: 'https://example.com/avatar1.jpg',
      };
      
      const testUser2: UserInfo = {
        id: 'user456',
        name: 'Jane Smith',
        avatar: 'https://example.com/avatar2.jpg',
      };
      
      console.log('👤 Test users created:', { testUser1, testUser2 });
      
      // Verify user data structure
      if (!testUser1.id || !testUser1.name) {
        throw new Error('Invalid user1 data');
      }
      
      if (!testUser2.id || !testUser2.name) {
        throw new Error('Invalid user2 data');
      }
      
      console.log('✅ User data test passed');
      return true;
      
    } catch (error) {
      console.error('❌ User data test failed:', error);
      return false;
    }
  }
  
  /**
   * Run all tests
   */
  static runAllTests(): boolean {
    console.log('🚀 Running WebRTC Service tests...');
    
    const tests = [
      this.testInitialization,
      this.testCallControls,
      this.testCallbacks,
      this.testUserData,
    ];
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    for (const test of tests) {
      try {
        if (test()) {
          passedTests++;
        }
      } catch (error) {
        console.error('❌ Test execution error:', error);
      }
    }
    
    console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All WebRTC Service tests passed!');
      return true;
    } else {
      console.log('⚠️ Some WebRTC Service tests failed');
      return false;
    }
  }
  
  /**
   * Mock call test (without actual WebRTC connection)
   */
  static async testMockCall(): Promise<boolean> {
    try {
      console.log('🧪 Testing mock call flow...');
      
      const caller: UserInfo = {
        id: 'caller123',
        name: 'Caller User',
        avatar: 'https://example.com/caller.jpg',
      };
      
      const receiver: UserInfo = {
        id: 'receiver456',
        name: 'Receiver User',
        avatar: 'https://example.com/receiver.jpg',
      };
      
      // Note: This will fail because socket is not connected
      // But we can test the initial setup
      console.log('📞 Testing call initiation (will fail without socket)...');
      
      try {
        await WebRTCService.startCall(receiver, caller);
      } catch (error) {
        console.log('⚠️ Expected error (no socket):', error);
      }
      
      // Test call rejection without active call
      console.log('📞 Testing call rejection without active call...');
      WebRTCService.rejectCall(); // Should handle gracefully
      
      // Test call end without active call
      console.log('📞 Testing call end without active call...');
      WebRTCService.endCall(); // Should handle gracefully
      
      console.log('✅ Mock call test completed');
      return true;
      
    } catch (error) {
      console.error('❌ Mock call test failed:', error);
      return false;
    }
  }
}

// Export for easy testing
export default WebRTCServiceTest;
