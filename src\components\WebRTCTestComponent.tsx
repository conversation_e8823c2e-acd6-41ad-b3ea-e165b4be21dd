/**
 * WebRTC Test Component
 * Simple component to test WebRTC functionality
 */

import React, { useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
} from 'react-native';
import { useWebRTCCall } from '../hooks/useWebRTCCall';
import { CallStatus, UserInfo } from '../services/webrtc/types/WebRTCTypes';
import WebRTCServiceTest from '../services/webrtc/WebRTCServiceTest';

const WebRTCTestComponent: React.FC = () => {
  const {
    callStatus,
    currentCall,
    callTimer,
    callControls,
    remoteStream,
    startCall,
    acceptCall,
    rejectCall,
    endCall,
    toggleMute,
    toggleSpeaker,
    error,
    clearError,
  } = useWebRTCCall();

  // Test users
  const testUser1: UserInfo = {
    id: 'user123',
    name: '<PERSON>',
    avatar: 'https://example.com/avatar1.jpg',
  };

  const testUser2: UserInfo = {
    id: 'user456',
    name: '<PERSON>',
    avatar: 'https://example.com/avatar2.jpg',
  };

  // Run tests on mount
  useEffect(() => {
    console.log('🧪 Running WebRTC Service tests...');
    WebRTCServiceTest.runAllTests();
  }, []);

  // Handle test call
  const handleTestCall = async () => {
    try {
      Alert.alert(
        'Test Call',
        'This will attempt to start a test call (may fail without socket connection)',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Start Call',
            onPress: async () => {
              await startCall(testUser2, testUser1);
            },
          },
        ]
      );
    } catch (error) {
      console.error('Test call error:', error);
    }
  };

  // Handle accept call
  const handleAcceptCall = async () => {
    try {
      await acceptCall();
    } catch (error) {
      console.error('Accept call error:', error);
    }
  };

  // Handle reject call
  const handleRejectCall = () => {
    try {
      rejectCall();
    } catch (error) {
      console.error('Reject call error:', error);
    }
  };

  // Handle end call
  const handleEndCall = () => {
    try {
      endCall();
    } catch (error) {
      console.error('End call error:', error);
    }
  };

  // Handle mute toggle
  const handleToggleMute = () => {
    const result = toggleMute();
    console.log('Mute toggled:', result);
  };

  // Handle speaker toggle
  const handleToggleSpeaker = () => {
    const result = toggleSpeaker();
    console.log('Speaker toggled:', result);
  };

  // Run service tests
  const handleRunTests = () => {
    const result = WebRTCServiceTest.runAllTests();
    Alert.alert(
      'Test Results',
      result ? 'All tests passed!' : 'Some tests failed. Check console for details.',
      [{ text: 'OK' }]
    );
  };

  // Format call duration
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Get status color
  const getStatusColor = (status: CallStatus): string => {
    switch (status) {
      case CallStatus.IDLE: return '#666';
      case CallStatus.CALLING: return '#ff9500';
      case CallStatus.RINGING: return '#007aff';
      case CallStatus.CONNECTING: return '#ff9500';
      case CallStatus.CONNECTED: return '#34c759';
      case CallStatus.ENDED: return '#666';
      case CallStatus.FAILED: return '#ff3b30';
      case CallStatus.TIMEOUT: return '#ff9500';
      default: return '#666';
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>WebRTC Test Component</Text>
      
      {/* Error Display */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Error: {error}</Text>
          <TouchableOpacity onPress={clearError} style={styles.clearErrorButton}>
            <Text style={styles.clearErrorText}>Clear</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Call Status */}
      <View style={styles.statusContainer}>
        <Text style={styles.statusLabel}>Call Status:</Text>
        <Text style={[styles.statusValue, { color: getStatusColor(callStatus) }]}>
          {callStatus.toUpperCase()}
        </Text>
      </View>

      {/* Current Call Info */}
      {currentCall && (
        <View style={styles.callInfoContainer}>
          <Text style={styles.sectionTitle}>Current Call</Text>
          <Text style={styles.callInfo}>ID: {currentCall.callHistoryId}</Text>
          <Text style={styles.callInfo}>Caller: {currentCall.caller.name}</Text>
          <Text style={styles.callInfo}>Receiver: {currentCall.receiver.name}</Text>
          <Text style={styles.callInfo}>Direction: {currentCall.direction}</Text>
        </View>
      )}

      {/* Call Timer */}
      {callTimer.isRunning && (
        <View style={styles.timerContainer}>
          <Text style={styles.timerLabel}>Call Duration:</Text>
          <Text style={styles.timerValue}>{formatDuration(callTimer.duration)}</Text>
        </View>
      )}

      {/* Call Controls Status */}
      <View style={styles.controlsContainer}>
        <Text style={styles.sectionTitle}>Call Controls</Text>
        <Text style={styles.controlInfo}>
          Muted: {callControls.isMuted ? 'Yes' : 'No'}
        </Text>
        <Text style={styles.controlInfo}>
          Speaker: {callControls.isSpeakerOn ? 'On' : 'Off'}
        </Text>
        <Text style={styles.controlInfo}>
          Audio Device: {callControls.currentAudioDevice}
        </Text>
      </View>

      {/* Remote Stream Status */}
      <View style={styles.streamContainer}>
        <Text style={styles.sectionTitle}>Remote Stream</Text>
        <Text style={styles.streamInfo}>
          Status: {remoteStream ? 'Connected' : 'Not Connected'}
        </Text>
        {remoteStream && (
          <Text style={styles.streamInfo}>
            Stream ID: {remoteStream.id}
          </Text>
        )}
      </View>

      {/* Test Buttons */}
      <View style={styles.buttonsContainer}>
        <TouchableOpacity onPress={handleRunTests} style={styles.testButton}>
          <Text style={styles.buttonText}>Run Service Tests</Text>
        </TouchableOpacity>

        <TouchableOpacity onPress={handleTestCall} style={styles.callButton}>
          <Text style={styles.buttonText}>Test Start Call</Text>
        </TouchableOpacity>

        {callStatus === CallStatus.RINGING && (
          <>
            <TouchableOpacity onPress={handleAcceptCall} style={styles.acceptButton}>
              <Text style={styles.buttonText}>Accept Call</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={handleRejectCall} style={styles.rejectButton}>
              <Text style={styles.buttonText}>Reject Call</Text>
            </TouchableOpacity>
          </>
        )}

        {(callStatus === CallStatus.CONNECTED || callStatus === CallStatus.CALLING) && (
          <TouchableOpacity onPress={handleEndCall} style={styles.endButton}>
            <Text style={styles.buttonText}>End Call</Text>
          </TouchableOpacity>
        )}

        {callStatus === CallStatus.CONNECTED && (
          <>
            <TouchableOpacity onPress={handleToggleMute} style={styles.muteButton}>
              <Text style={styles.buttonText}>
                {callControls.isMuted ? 'Unmute' : 'Mute'}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={handleToggleSpeaker} style={styles.speakerButton}>
              <Text style={styles.buttonText}>
                {callControls.isSpeakerOn ? 'Speaker Off' : 'Speaker On'}
              </Text>
            </TouchableOpacity>
          </>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: 10,
    borderRadius: 8,
    marginBottom: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  errorText: {
    color: '#c62828',
    flex: 1,
  },
  clearErrorButton: {
    backgroundColor: '#c62828',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 4,
  },
  clearErrorText: {
    color: 'white',
    fontSize: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    padding: 15,
    backgroundColor: 'white',
    borderRadius: 8,
  },
  statusLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 10,
    color: '#333',
  },
  statusValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  callInfoContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  callInfo: {
    fontSize: 14,
    marginBottom: 5,
    color: '#666',
  },
  timerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
    padding: 15,
    backgroundColor: '#e8f5e8',
    borderRadius: 8,
  },
  timerLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 10,
    color: '#2e7d32',
  },
  timerValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2e7d32',
  },
  controlsContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
  },
  controlInfo: {
    fontSize: 14,
    marginBottom: 5,
    color: '#666',
  },
  streamContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  streamInfo: {
    fontSize: 14,
    marginBottom: 5,
    color: '#666',
  },
  buttonsContainer: {
    gap: 10,
  },
  testButton: {
    backgroundColor: '#6c757d',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  callButton: {
    backgroundColor: '#007bff',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  acceptButton: {
    backgroundColor: '#28a745',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  rejectButton: {
    backgroundColor: '#dc3545',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  endButton: {
    backgroundColor: '#dc3545',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  muteButton: {
    backgroundColor: '#ffc107',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  speakerButton: {
    backgroundColor: '#17a2b8',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default WebRTCTestComponent;
